import json
from typing import Optional

from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.company_agent import search_company
from agent.search_apollo_tools import search_apollo_people
from agent.search_linkedin_tools import get_linkedin_profiles
from apollo.apollo_api import bulk_people_enrichment
from utils import init_model
from utils.file_handler import load_file
from zoho.accounts_api import fetch_accounts_info_by_account_id
from zoho.get_existing_contacts import get_existing_contacts


async def search_contact_agent(apollo_organization_id: str, user_input: str, exclude_contacts) -> list[dict]:
    """
    Query Contact Agent

    args:
        apollo_organization_id: Apollo organization ID
        user_input: User input for searching contacts
        exclude_contacts: List of contacts to exclude from the search

    return:
        List of contacts matching the search criteria
    """
    try:
        tools = [
            search_apollo_people,
            get_linkedin_profiles,
        ]

        system_prompt = load_file("agent/contact/prompt/contact_intelligence_agent.md")
        if not system_prompt:
            logger.error("Failed to load system prompt")
            return []

        prompts = ChatPromptTemplate.from_messages(
            [SystemMessagePromptTemplate.from_template(system_prompt), ("placeholder", "{messages}")]
        )

        llm = init_model(model="gpt-4.1", temperature=0.1, max_tokens=4096)

        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=prompts,
            name="intelligence_contact_agent",
        )

        # 构建更详细的消息
        messages = [
            f"用户查询需求：{user_input}",
            f"Apollo企业ID: {apollo_organization_id}",
            f"已有联系人信息（需要排除）: {exclude_contacts}",
            "",
            "请按照以下步骤执行：",
            "1. 使用 search_apollo_people 工具搜索该企业的员工信息",
            "2. 根据用户需求筛选最匹配的候选人",
            "3. 如需要，使用 get_linkedin_profiles 获取详细信息",
            "4. 排除已有联系人，避免重复",
            "5. 返回最符合条件的联系人列表（最多5个）",
            "",
            "重要：必须返回纯 JSON 数组格式，不要包含任何其他文本或标记符号。",
            "如果没有找到符合条件的联系人，请返回空数组 []。",
        ]

        result = await agent.ainvoke({"messages": messages})

        logger.info(f"Sales Agent search_contact_agent result: {result}")

        # 获取最后一条消息的内容
        if not result or "messages" not in result or not result["messages"]:
            logger.error("Sales Agent search_contact_agent: No messages in result")
            return []

        content = result["messages"][-1].content
        logger.info(f"Sales Agent search_contact_agent content: {content}, type: {type(content)}")

        # 检查是否有错误
        if '"error"' in content or "error" in content.lower():
            logger.error(f"Sales Agent search_contact_agent error: {content}")
            return []

        # 尝试解析 JSON
        try:
            # 清理内容，移除可能的标记符号
            cleaned_content = content.strip()
            if cleaned_content.startswith("```json"):
                cleaned_content = cleaned_content[7:]
            if cleaned_content.endswith("```"):
                cleaned_content = cleaned_content[:-3]
            cleaned_content = cleaned_content.strip()

            # 解析 JSON
            contacts = json.loads(cleaned_content)

            # 确保返回的是列表
            if isinstance(contacts, dict):
                contacts = [contacts]
            elif not isinstance(contacts, list):
                logger.error(f"Sales Agent search_contact_agent: Expected list or dict, got {type(contacts)}")
                return []

            logger.info(f"Sales Agent search_contact_agent: Successfully parsed {len(contacts)} contacts")
            return contacts

        except json.JSONDecodeError as e:
            logger.error(f"Sales Agent search_contact_agent: Failed to parse JSON: {e}")
            logger.error(f"Sales Agent search_contact_agent: Content was: {content}")
            return []

    except Exception as ex:
        logger.error(f"Sales Agent Error in search_contact_agent: {ex}", exc_info=True)
        return []


async def intelligence_search_contact(account_id, user_input: str, user_info: Optional[dict] = None) -> list[dict]:
    """
    search contact intelligence information

    Args:
        account_id: Zoho account ID
        user_input: User input for searching contacts
        user_info: Optional user information, e.g., email

    Returns:
        List of enriched contact information matching the search criteria

    """
    try:
        # 检查输入参数
        if not account_id or not user_input:
            raise ValueError("contact_id, user_input are required")

        zoho_account_info = await fetch_accounts_info_by_account_id(account_id)

        if not zoho_account_info:
            logger.error("Cannot find zoho account info for account_id: {account_id}")
            return []

        company_name = zoho_account_info.get("name")
        contact_location = zoho_account_info.get("territory")
        organization_website = zoho_account_info.get("website")

        logger.info(f"zoho_account_info: {zoho_account_info}")

        if not company_name:
            logger.error(f"Missing company name in zoho account info for account_id: {account_id}")
            return []

        current_user_email = user_info.get("email") if user_info else None
        logger.info(f"current_user_email: {current_user_email}")

        # langfuse = get_client()
        # langfuse.update_current_trace(
        #     user_id=current_user_email,
        #     metadata={
        #         "account_id": account_id,
        #     },
        # )

        # query company information
        apollo_company_info = await search_company(
            company_name=company_name,
            company_location=contact_location or "",
            organization_website=organization_website or "",
        )

        logger.info(f"apollo_company_info: {apollo_company_info}")
        if apollo_company_info is None:
            logger.error(f"Cannot find company information for {company_name} on Apollo")
            return []

        exclude_contacts = await get_existing_contacts(company_name, account_id)
        if not exclude_contacts:
            logger.warning(f"No existing contacts found for company: {company_name}")
        logger.info(f"exclude_contacts count: {len(exclude_contacts)}")

        apollo_organization_id = apollo_company_info["id"]

        logger.info(f"apollo_organization_id: {apollo_organization_id}")

        if apollo_organization_id is None:
            logger.error(f"Cannot find apollo organization id for {company_name}")
            return []

        result = await search_contact_agent(apollo_organization_id, user_input, exclude_contacts)

        logger.info(f"search_contact_agent result: {result}")

        if not result or len(result) == 0:
            return []

        enrich_contact = bulk_people_enrichment(result)
        logger.info(f"Sales Agent enrich_contact: {enrich_contact}")

        return enrich_contact
    except Exception as e:
        logger.error(f"搜索联系人智能信息失败: {e}", exc_info=True)
        raise Exception(f"搜索联系人智能信息失败: {str(e)}")


if __name__ == "__main__":
    import asyncio

    from dotenv import load_dotenv

    load_dotenv()

    result = asyncio.run(intelligence_search_contact("3091799000306481001", "找到一个销售经理"))
    logger.info(result)
