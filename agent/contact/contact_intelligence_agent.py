from pprint import pprint
from typing import Optional

from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate
from langfuse import get_client
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.company_agent import search_company
from agent.contact.models import SearchContactsR<PERSON>ult
from agent.search_apollo_tools import search_apollo_people
from agent.search_linkedin_tools import get_linkedin_profiles
from apollo.apollo_api import bulk_people_enrichment
from utils import init_model
from utils.extractors import extract_structure_response
from utils.file_handler import load_file
from zoho.accounts_api import fetch_accounts_info_by_account_id
from zoho.get_existing_contacts import get_existing_contacts


async def search_contact_agent(apollo_organization_id: str, user_input: str, exclude_contacts):
    """
    Query Contact Agent

    args:
        apollo_organization_id: Apollo organization ID
        user_input: User input for searching contacts
        exclude_contacts: List of contacts to exclude from the search

    return:
        List of contacts matching the search criteria
    """
    try:
        tools = [
            search_apollo_people,
            get_linkedin_profiles,
        ]

        system_prompt = load_file("agent/contact/prompt/contact_intelligence_agent.md")

        prompts = ChatPromptTemplate.from_messages(
            [SystemMessagePromptTemplate.from_template(system_prompt), ("placeholder", "{messages}")]
        )

        llm = init_model(model="gpt-4.1", temperature=0.1, max_tokens=2048)

        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=prompts,
            name="intelligence_contact_agent",
        )

        result = await agent.ainvoke(
            {
                "messages": [
                    f"用户查询需求：{user_input}",
                    f"Apollo企业ID: {apollo_organization_id}",
                    f"已有联系人信息: {exclude_contacts}",
                    "请根据用户查询需求，搜索联系人信息，并返回符合条件的联系人列表。",
                    "如果没有找到符合条件的联系人，请返回空列表。",
                ]
            }
        )

        logger.info(f"search_contact_agent result: {result}")

        content = result.get("messages")[-1].text()
        logger.info(f"search_contact_agent content: {content}, type: {type(content)}")

        if '"error"' in content:
            logger.error(f"search_contact_agent error: {content}")
            return []

        return content

    except Exception as ex:
        logger.error(f"Error in search_contact_agent: {ex}", exc_info=True)
        return []


async def intelligence_search_contact(account_id, user_input: str, user_info: Optional[dict] = None) -> list[dict]:
    """
    search contact intelligence information

    Args:
        account_id: Zoho account ID
        user_input: User input for searching contacts
        user_info: Optional user information, e.g., email

    Returns:
        List of enriched contact information matching the search criteria

    """
    try:
        # 检查输入参数
        if not account_id or not user_input:
            raise ValueError("contact_id, user_input are required")

        zoho_account_info = await fetch_accounts_info_by_account_id(account_id)

        if not zoho_account_info:
            logger.error("Cannot find zoho account info for account_id: {account_id}")
            return []

        company_name = zoho_account_info.get("name")
        contact_location = zoho_account_info.get("territory")
        organization_website = zoho_account_info.get("website")

        logger.info(f"zoho_account_info: {zoho_account_info}")

        if not company_name:
            logger.error(f"Missing company name in zoho account info for account_id: {account_id}")
            return []

        current_user_email = user_info.get("email") if user_info else None
        logger.info(f"current_user_email: {current_user_email}")

        # langfuse = get_client()
        # langfuse.update_current_trace(
        #     user_id=current_user_email,
        #     metadata={
        #         "account_id": account_id,
        #     },
        # )

        # query company information
        apollo_company_info = await search_company(
            company_name=company_name,
            company_location=contact_location or "",
            organization_website=organization_website or "",
        )

        logger.info(f"apollo_company_info: {apollo_company_info}")
        if apollo_company_info is None:
            logger.error(f"Cannot find company information for {company_name} on Apollo")
            return []

        exclude_contacts = await get_existing_contacts(company_name, account_id)
        if not exclude_contacts:
            logger.warning(f"No existing contacts found for company: {company_name}")
        logger.info(f"exclude_contacts count: {len(exclude_contacts)}")

        apollo_organization_id = apollo_company_info["id"]

        logger.info(f"apollo_organization_id: {apollo_organization_id}")

        if apollo_organization_id is None:
            logger.error(f"Cannot find apollo organization id for {company_name}")
            return []

        result = await search_contact_agent(apollo_organization_id, user_input, exclude_contacts)

        logger.info(f"search_contact_agent result: {result}")

        if not result or len(result) == 0:
            return []

        enrich_contact = await bulk_people_enrichment(result)
        logger.info(f"enrich_contact: {enrich_contact}")

        return enrich_contact
    except Exception as e:
        logger.error(f"搜索联系人智能信息失败: {e}", exc_info=True)
        raise Exception(f"搜索联系人智能信息失败: {str(e)}")


if __name__ == "__main__":
    import asyncio

    from dotenv import load_dotenv

    load_dotenv()

    result = asyncio.run(intelligence_search_contact("3091799000306481001", "找到一个销售经理"))
    logger.info(result)
