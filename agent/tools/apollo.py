import asyncio
import json
import os
from dataclasses import dataclass
from pathlib import Path
from typing import Any, List, Optional, TypedDict

import httpx
import yaml
from langchain_core.messages import AIMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent
from loguru import logger
from pydantic import BaseModel, Field

from utils import Cache
from utils.models import init_model

# 为Apollo API创建缓存
cache = Cache(
    directory=Path(__file__).parent / ".cache/apollo",
    timeout=24 * 60 * 60,
    cache_enabled_env_var="APOLLO_CACHE_ENABLED",
)


class PrimaryPhone(TypedDict, total=False):
    number: str
    source: str
    sanitized_number: str


class ApolloOrganization(TypedDict, total=False):
    # example: "5fc8577084e9630001657d81"
    id: str
    # example: "Pilot Airport Hotel"
    name: str
    # example: "http://www.airporthotelpilot.fi"
    website_url: str | None
    # example: None
    blog_url: str | None
    # example: None
    angellist_url: str | None
    # example: "http://www.linkedin.com/company/pilot-airport-hotel"
    linkedin_url: str | None
    # example: None
    twitter_url: str | None
    # example: "https://www.facebook.com/PilotAirportHotel/"
    facebook_url: str | None
    # example: {"number": "+358 93 294800", "source": "Scraped", "sanitized_number": "+***********"}
    primary_phone: PrimaryPhone | None
    # example: []
    languages: list[str]
    # example: None
    alexa_ranking: int | None
    # example: "+358 93 294800"
    phone: str | None
    # example: "66973397"
    linkedin_uid: str | None
    # example: None
    founded_year: int | None
    # example: None
    publicly_traded_symbol: str | None
    # example: None
    publicly_traded_exchange: str | None
    # example: "https://zenprospect-production.s3.amazonaws.com/uploads/pictures/6710b448b60e760001c5a641/picture"
    logo_url: str | None
    # example: None
    crunchbase_url: str | None
    # example: "airporthotelpilot.fi"
    primary_domain: str | None
    # example: ["7011"]
    sic_codes: list[str]
    # example: ["72111"]
    naics_codes: list[str]
    # example: "+***********"
    sanitized_phone: str | None
    # example: None
    owned_by_organization_id: str | None
    # example: None
    organization_revenue_printed: str | None
    # example: 0.0
    organization_revenue: float
    # example: None
    intent_strength: float | None
    # example: True
    show_intent: bool
    # example: False
    has_intent_signal_account: bool
    # example: None
    intent_signal_account: dict | None
    # example: None
    organization_headcount_six_month_growth: float | None
    # example: None
    organization_headcount_twelve_month_growth: float | None
    # example: None
    organization_headcount_twenty_four_month_growth: float | None


class SimplifiedOrganization(TypedDict, total=False):
    name: str
    id: str
    website_url: str | None
    linkedin_url: str | None
    founded_year: str | None
    organization_revenue: str | None
    phone: str | None


@dataclass
class PaginationInfo:
    page: int
    per_page: int
    total_entries: int
    total_pages: int

    @staticmethod
    def from_dict(data: dict) -> "PaginationInfo":
        return PaginationInfo(
            page=data.get("page"),
            per_page=data.get("per_page"),
            total_entries=data.get("total_entries"),
            total_pages=data.get("total_pages"),
        )


@dataclass
class OrganizationSearchResponse:
    data: list[SimplifiedOrganization] | None = None
    pagination: PaginationInfo | None = None
    error: str | None = None


def is_not_default_value(value: Any) -> bool:
    """
    Check if the value is the default value
    """
    return value is not None and value != "" and value != []


class ApolloAPIClient:
    """
    Apollo API客户端类，封装所有API调用功能
    """

    def __init__(self):
        self._client: httpx.AsyncClient | None = None
        self._semaphore = asyncio.Semaphore(2)

    @property
    def client(self) -> httpx.AsyncClient:
        """
        懒加载创建HTTP客户端
        """
        if self._client is None:
            # check required environment variables
            apollo_api_key = os.environ.get("APOLLO_API_KEY")

            if not apollo_api_key:
                raise ValueError("APOLLO_API_KEY environment variable is required")

            self._client = httpx.AsyncClient(
                timeout=30.0,
                headers={"X-Api-Key": apollo_api_key},
                base_url="https://api.apollo.io/api/v1",
                proxy=os.environ.get("OPENAI_PROXY", None),
            )
            logger.info("Apollo API client initialized")
        return self._client

    @cache.memoize()
    async def search_organization(self, **kwargs) -> OrganizationSearchResponse:
        """
        搜索企业信息
        Args:
            **kwargs: 搜索参数
        Returns:
            包含企业信息和分页信息的字典或错误信息
        """

        async with self._semaphore:
            data = {
                **kwargs,
                "per_page": 100,
            }

            response = await self.client.post("/mixed_companies/search", json=data)

            if response.status_code == 200:
                result = response.json()
                organizations = result.get("organizations", [])

                organizations = self._filter_organizations(organizations, kwargs.get("exclude_organization_names", []))
                simplified_organizations = self.simplify_organization_results(organizations)

                # create structured response
                pagination = PaginationInfo.from_dict(result.get("pagination", {}))
                return OrganizationSearchResponse(data=simplified_organizations, pagination=pagination)
            else:
                error_msg = f"Error: {response.status_code}, {response.text}"
                logger.warning(f"failed to search organizations: {error_msg}")
                return OrganizationSearchResponse(error=error_msg)

    def _simplify_organization_info(self, organization: dict) -> dict:
        """
        Simplify organization info, remove the fields that are not needed, etc: current_technologies
        """

    def _simplify_organization_info(self, organization: dict) -> dict:
        """
        remove the 'current_technologies' field from organization info if present
        """
        # make a shallow copy to avoid mutating input
        exclude_fields = [
            "current_technologies",
            "industry_tag_hash",
            "industry_tag_id",
            "logo_url",
            "naics_codes",
            "sic_codes",
            "primary_phone",
            "employee_metrics",
        ]
        return {
            field: value
            for field, value in organization.items()
            if field not in exclude_fields and is_not_default_value(value)
        }

    def _filter_organizations(
        self, organizations: list[ApolloOrganization], exclude_organization_names: list[str]
    ) -> list[ApolloOrganization]:
        """
        Filter organizations by excluding the names in exclude_organization_names
        """
        if len(organizations) == 0 or len(exclude_organization_names) == 0:
            return organizations

        return [org for org in organizations if org.get("name", "") not in exclude_organization_names]

    @cache.memoize()
    async def get_organization_by_id(self, organization_id: str) -> dict:
        """
        通过组织 ID 获取详细的组织信息
        Args:
            organization_id: 组织的唯一标识符
        Returns:
            包含组织详细信息的字典或错误信息
        """

        async with self._semaphore:
            logger.debug(f"send request to get organization by id: {organization_id}")

            response = await self.client.get(f"/organizations/{organization_id}")

            if response.status_code == 200:
                logger.debug(f"successfully fetch organization details for id: {organization_id}")
                result = response.json()

                organization = result.get("organization", {})
                if "id" not in organization:
                    return {"error": "organization not found"}

                return self._simplify_organization_info(organization)
            else:
                error_msg = f"Error: {response.status_code}, {response.text}"
                logger.warning(f"failed to get organization by id {organization_id}: {error_msg}")
                return {"error": error_msg}

    async def search_people(
        self,
        q_keywords: str | None = None,
        organization_ids: list[str] | None = None,
        person_titles: list[str] = [],
        seniorities: list[str] = [],
        locations: list[str] = [],
        q_organization_job_titles: list[str] | None = None,
        page: int = 1,
        **kwargs,
    ) -> dict:
        """
        搜索人员信息
        Args:
            q_keywords: A string of words over which we want to filter the results.
            organization_ids: 公司ID列表
            person_titles: 职位标题列表
            seniorities: 级别列表
            locations: 位置列表
            q_organization_job_titles: 公司职位列表
            page: 页码
        Returns:
            包含人员信息的字典或错误信息
        """
        async with self._semaphore:
            # print search parameters dynamically
            search_params = {
                "q_keywords": q_keywords,
                "organization_ids": organization_ids,
                "person_titles": person_titles,
                "seniorities": seniorities,
                "locations": locations,
                "q_organization_job_titles": q_organization_job_titles,
                "page": page,
                **kwargs,
            }
            # filter out None and empty values for cleaner logging
            active_params = {k: v for k, v in search_params.items() if v is not None and v != []}
            logger.info(f"search people with params: {active_params}")

            # 构建请求体
            data = active_params | {
                "per_page": 100,
            }

            response = await self.client.post("/mixed_people/search", json=data)

            if response.status_code == 200:
                logger.debug("successfully fetch people search results")
                result = response.json()
                people = result.get("people", [])
                if people:
                    people = self.simplify_people_results(people)
                    result["people"] = people

                return result
            else:
                error_msg = f"Error: {response.status_code}, {response.text}"
                logger.warning(f"failed to search people: {error_msg}")
                return {"error": error_msg}

    @staticmethod
    def simplify_organization_results(organizations: list[ApolloOrganization]) -> list[SimplifiedOrganization]:
        """
        简化企业搜索结果
        """
        return [
            SimplifiedOrganization(
                name=item.get("name", ""),
                id=item["id"],
                website_url=item.get("website_url", ""),
                linkedin_url=item.get("linkedin_url", ""),
                founded_year=item.get("founded_year", ""),
                organization_revenue=item.get("organization_revenue_printed", ""),
                phone=item.get("phone", None),
            )
            for item in organizations
        ]

    @staticmethod
    def simplify_people_results(people: list) -> list:
        """
        简化人员搜索结果
        """
        simplified_results = []
        for person in people:
            simplified_person = {
                "name": f"{person.get('first_name', '')} {person.get('last_name', '')}",
                "title": person.get("title", ""),
                "company": person.get("organization", {}).get("name", ""),
                "location": person.get("location", {}).get("city", "") or person.get("city", ""),
                "country": person.get("location", {}).get("country", "") or person.get("country", ""),
                "linkedin_url": person.get("linkedin_url", ""),
                "seniority": person.get("seniority", ""),
                "department": person.get("department", ""),
            }
            simplified_results.append(simplified_person)
        return simplified_results

    async def close(self):
        """
        关闭HTTP客户端连接
        """
        if self._client is not None:
            await self._client.aclose()
            self._client = None

    async def search_organization_id(self, name: str, location: str, website_url: str) -> dict:
        # If no ID provided, need to search first
        if not any([name, location, website_url]):
            raise Exception("must provide at least one of id, name, location, or website_url")

        # Create a react agent to search for the organization
        llm = init_model(model="gemini-2.5-flash", thinking_budget=128)

        # Create tools for the agent
        tools = [search_organizations]

        # Create system prompt for the agent
        system_prompt = """你是一个专业的企业信息搜索助手。你的任务是根据提供的信息找到对应的组织，并获取其完整详细信息。

工作流程：
1. 根据提供的信息（名称、位置、网站等）使用 search_organizations 工具搜索相关组织
2. 从搜索结果中选择最匹配的组织
3. 如果搜索结果有多个组织，请选择最匹配提供信息的那个
4. 如果搜索结果没有组织，再尝试调整搜索条件再搜索一次，如修改公司的名称，或者修改位置
5. 返回该组织的 ID

## 搜索策略

### 1. 简化名称
- 去除公司后缀：移除"有限公司"、"Inc."、"Corp"、"Ltd"等
- 去除地域词：移除城市名、国家名等
- 去除通用词：移除"科技"、"技术"、"集团"、"控股"等
- 提取核心品牌名
- 使用名称的不同形式

### 2. 关键词拆分
- 单独搜索核心词汇
- 使用名称前半部分或后半部分
- 优先使用知名度高的品牌词

### 3. 同义词替换
- 替换行业术语（如"信息"→"数据"）
- 尝试常见缩写或全称
- 考虑业内常用别名

### 4. 其它
- Try to use domain name as organization name to search if use real name can not find the organization
- Prefer to use domain name as organization name to search first if website_url is provided
- Don't use location and website_url to search if domain name is provided, domain name is enough to locate the organization

找到了组织时，输出示例：
{{
"explanation": "<explanation of the decision>",
"id": "60b2d0545e799d0001881d19", # 组织的唯一标识符
}}

没有找到组织时，输出示例：
{{
"error": "<error message, provide detailed error message>"
}}

输出要求：
- 输出格式为 JSON 格式
- 不要有任何其它内容和解释
- 不要使用任何标记符号，如 ```json 或 ```

注意事项：
- 如果搜索结果有多个组织，请选择最匹配提供信息的那个，但不要选择与用户提供的信息明显不匹配的组织
- 智能判断提供的网站是否与组织匹配，不要选择明显不匹配的组织
- 所有搜索参数必须使用英文
- 确保返回的是完整的组织详细信息，而不是搜索结果摘要
- 你必须动态调整搜索条件，多轮搜索，尽可能找到最匹配的组织
- 在返回之前，Double Check 一下搜索结果，确保没有明显错误
- User provided fields are not always correct, you need to adjust the search conditions to find the most matching organization
"""  # noqa: E501

        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                ("placeholder", "{messages}"),
            ]
        )

        # Create the react agent
        agent = create_react_agent(model=llm, tools=tools, prompt=prompt, name="get_complete_organization_info")

        # Prepare input message
        search_params = []
        if name:
            search_params.append(f"组织名称: {name}")
        if location:
            search_params.append(f"位置: {location}")
        if website_url:
            search_params.append(f"网站: {website_url}")

        input_message = "请帮我找到以下组织的完整详细信息：\n" + "\n".join(search_params)

        # Execute the agent
        response = await agent.ainvoke({"messages": [("human", input_message)]})

        message = response.get("messages")[-1]
        if isinstance(message, AIMessage):
            result = JsonOutputParser().parse(message.text())
            if "id" in result:
                return result.get("id")
            else:
                raise Exception(
                    f"can not find the specific organization from apollo: {result.get('error', 'unknown error')}"
                )
        raise Exception("failed to get organization id")

    @cache.memoize(expire=60 * 60)
    async def enrich_person(
        self,
        first_name: str | None = None,
        last_name: str | None = None,
        name: str | None = None,
        email: str | None = None,
        organization_name: str | None = None,
        domain: str | None = None,
        id: str | None = None,
        linkedin_url: str | None = None,
        reveal_personal_emails: bool = False,
        reveal_phone_number: bool = False,
        webhook_url: str | None = None,
    ) -> dict:
        """
        Enrich data for 1 person using Apollo People Enrichment API
        Args:
            first_name: The first name of the person.
            last_name: The last name of the person.
            name: The full name of the person.
            email: The email of the person.
            organization_name: The name of the organization.
            domain: The domain of the organization.
            id: The Apollo ID of the person.
            linkedin_url: The LinkedIn URL of the person.
            reveal_personal_emails: Whether to reveal the personal emails of the person.
            reveal_phone_number: Whether to reveal the phone number of the person.
            webhook_url: The webhook URL to receive the phone number. If the reveal_phone_number parameter is true, this parameter becomes mandatory.  # noqa: E501
        Returns:
            包含人员丰富信息的字典或错误信息
        """
        async with self._semaphore:
            # filter out None values
            data = {
                k: v
                for k, v in {
                    "first_name": first_name,
                    "last_name": last_name,
                    "name": name,
                    "email": email,
                    "organization_name": organization_name,
                    "domain": domain,
                    "id": id,
                    "linkedin_url": linkedin_url,
                    "reveal_personal_emails": reveal_personal_emails if reveal_personal_emails else None,
                    "reveal_phone_number": reveal_phone_number if reveal_phone_number else None,
                    "webhook_url": webhook_url if reveal_phone_number else None,
                }.items()
                if v is not None
            }
            logger.info(f"enrich person with params: {data}")

            response = await self.client.post("/people/match", json=data)

            if response.status_code == 200:
                logger.debug("successfully enriched person data")
                result = response.json()
                person = result.get("person", {})

                if person:
                    # simplify person result if needed
                    return person
                else:
                    return {"error": "No person data found in response"}
            else:
                error_msg = f"Error: {response.status_code}, {response.text}"
                logger.warning(f"failed to enrich person: {error_msg}")
                return {"error": error_msg}

    async def get_complete_organization_info(
        self,
        id: str | None = None,
        name: str | None = None,
        location: str | None = None,
        website_url: str | None = None,
    ) -> dict:
        """
        Get complete organization information by ID, name, location, or website URL.
        If ID is provided, directly fetch the organization details.
        If ID is not provided, use an AI agent to search for the organization first, then get detailed information.

        Args:
            id: The unique Apollo ID of the organization
            name: The name of the organization to search for
            location: The location of the organization (city, state, or country)
            website_url: The website URL of the organization

        Returns:
            Complete organization information or error message in dict format
        """
        logger.info(
            f"get complete organization info - ID: {id}, Name: {name}, Location: {location}, Website: {website_url}"
        )

        if not id:
            id = await self.search_organization_id(name=name, location=location, website_url=website_url)

        return await self.get_organization_by_id(id)


# 全局API客户端实例
apollo = ApolloAPIClient()


class SearchOrganizationsSchema(BaseModel):
    """搜索组织的参数模型"""

    q_organization_name: Optional[str] = Field(
        default=None,
        description="""Filter search results to include a specific company name or domain name.""",
    )
    q_organization_keyword_tags: Optional[List[str]] = Field(
        default=None,
        description="""Filter search results based on keywords associated with companies.
        For example, you can enter mining as a value to return only companies that have an association with the mining industry.
        Always use English keywords.

        To improve search results, include multiple variants of the same keyword when possible:
        - For "air compressor manufacturer", also include "air compressor", "compressor"
        - For "software development", also include "software", "development", "programming"
        - For "data analytics", also include "data", "analytics", "business intelligence"

        """,  # noqa: E501
    )
    organization_locations: Optional[List[str]] = Field(
        default=None,
        description="""The location of the company headquarters.
        You can search across cities, US states, and countries.""",  # noqa: E501
    )
    organization_num_employees_ranges: Optional[List[str]] = Field(
        default=None,
        description="""The number range of employees working for the company. This enables you to find companies based on headcount. You can add multiple ranges to expand your search results.
            Each range you add needs to be a string, with the upper and lower numbers of the range separated only by a comma.
            Examples 1,10; 250,500; 10000,20000""",  # noqa: E501
    )


@tool(args_schema=SearchOrganizationsSchema)
async def search_organizations(
    q_organization_name: str | None = None,
    q_organization_keyword_tags: list[str] | None = None,
    organization_locations: list[str] | None = None,
    organization_num_employees_ranges: list[str] | None = None,
) -> str:
    """
    Use the Organization Search endpoint to find companies in the Apollo database. Several filters are available to help narrow your search.
    This method only provides company name, company website, company headquarters address, and LinkedIn company homepage URL.
    All of the params must be in English.

    Args:
        q_organization_name: Filter search results to include a specific company name.
        q_organization_keyword_tags: Filter search results based on keywords associated with companies.
        organization_locations: The location of the company headquarters.
        organization_num_employees_ranges: The number range of employees working for the company.

    Returns:
        Search results in YAML format
    """  # noqa: E501
    # print search parameters dynamically
    search_params = {
        "q_organization_name": q_organization_name,
        "q_organization_keyword_tags": q_organization_keyword_tags,
        "organization_locations": organization_locations,
        "organization_num_employees_ranges": organization_num_employees_ranges,
    }
    # filter out None values for cleaner logging
    active_params = {k: v for k, v in search_params.items() if v is not None}
    logger.info(f"search organizations with params: {active_params}")

    try:
        result = await apollo.search_organization(
            q_organization_name=q_organization_name,
            q_organization_keyword_tags=q_organization_keyword_tags,
            organization_locations=organization_locations,
            organization_num_employees_ranges=organization_num_employees_ranges,
        )
    except Exception as e:
        logger.opt(exception=e).error("search organizations error")
        return json.dumps({"error": str(e)})

    if result.error:
        return result.error
    else:
        logger.info(f"search organizations result: {len(result.data)} organizations found")
        # filter fields in result list
        return f"Total {len(result.data)} organizations found\n\n```yaml\n{yaml.dump(result.data, indent=2)}\n```"


@tool(parse_docstring=True)
async def get_organization_by_id(
    organization_id: str,
) -> str:
    """
    Get detailed information about a specific organization by its Apollo ID.
    This method provides comprehensive organization details including company information, contacts, and more.

    Args:
        organization_id: The unique Apollo ID of the organization to retrieve.

    Returns:
        Organization details in YAML format
    """
    logger.info(f"get organization by id: {organization_id}")

    try:
        result = await apollo.get_organization_by_id(organization_id)
    except Exception as e:
        logger.error(f"get organization by id error: {str(e)}")
        return json.dumps({"error": str(e)})

    if "error" in result:
        return result["error"]
    else:
        logger.info(f"get organization by id success: {result.get('name', 'Unknown')}")
        return f"```yaml\n{yaml.dump(result, indent=2)}\n```"


@tool(parse_docstring=True)
async def search_people(
    organization_ids: list[str],
    person_titles: list[str] | None = None,
    seniorities: list[str] | None = None,
    locations: list[str] | None = None,
    page: int = 1,
) -> str:
    """
    使用Apollo API搜索人员信息，支持分页功能。当结果数量超过单页限制时，可以通过调整page参数获取更多结果。
    如果第一页结果不足，可以使用page=2,3...等获取后续页面的数据。每次调用返回指定limit数量的结果。
    All the params must be in English.

    Args:
        organization_ids: List of company IDs to search for. For example, ["1234567890", "1234567891"]
        person_titles: Job titles held by the people you want to find. For a person to be included in search results,
            they only need to match 1 of the job titles you add. Adding more job titles expands your search results.
            Results also include job titles with the same terms, even if they are not exact matches.
            Must be in English.
            For example, searching for marketing manager might return people with the job title content marketing manager.
        seniorities: Job levels that you want to include in the search results.
            Only include job levels that are relevant to your search.
            For example, searching for VP might return people with the job title vice president.
            The following options can be used for this parameter,
            owner, founder, c_suite, partner, vp, head, director, manager, senior, entry, intern
        locations: The location where people live. You can search across cities, US states, and countries.
        page: The page number of the Apollo data that you want to retrieve.

    Returns:
        A JSON string containing the search results
    """  # noqa: E501
    # print search parameters dynamically
    search_params = {
        "organization_ids": organization_ids,
        "person_titles": person_titles,
        "seniorities": seniorities,
        "locations": locations,
        "page": page,
    }
    # filter out None values for cleaner logging
    active_params = {k: v for k, v in search_params.items() if v is not None}
    logger.info(f"search people with params: {active_params}")

    try:
        result = await apollo.search_people(
            organization_ids=organization_ids,
            person_titles=person_titles,
            seniorities=seniorities,
            locations=locations,
            page=page,
        )

        if "error" in result:
            return json.dumps({"error": result["error"]})

        people = result.get("people", [])
        page_info = result.get("pagination", {})
        total_entries = page_info.get("total_entries", 0)

        if total_entries > 200:
            return f"search people result: {total_entries} entries, more than 200, please adjust search conditions or page parameters."  # noqa: E501

        logger.info(
            f"search people result: {len(people)} entries, page {page}, total {page_info.get('total_pages', 0)} pages"  # noqa: E501
        )

        yaml_str = yaml.dump(
            {
                "pagination": page_info,
                "people": people,
            },
            indent=2,
        )
        return f"""
```yaml
{yaml_str}
```
{f"Total {total_entries} entries which is too few, please adjust search conditions or page parameters." if total_entries < 20 else ""}
"""  # noqa: E501

    except Exception as e:
        error_msg = f"search people error: {str(e)}"
        logger.error(error_msg)
        return json.dumps({"error": error_msg})


@tool(parse_docstring=True)
async def enrich_person(
    first_name: str | None = None,
    last_name: str | None = None,
    name: str | None = None,
    email: str | None = None,
    organization_name: str | None = None,
    domain: str | None = None,
    id: str | None = None,
    linkedin_url: str | None = None,
    reveal_personal_emails: bool = False,
    reveal_phone_number: bool = False,
    webhook_url: str | None = None,
) -> str:
    """
    Use the People Enrichment endpoint to enrich data for 1 person.
    Apollo relies on the information you pass via the endpoint's parameters to identify the correct person to enrich.
    If you provide more information about a person, Apollo is more likely to find a match within its database.

    Args:
        first_name: The first name of the person. This is typically used in combination with the last_name parameter.
        last_name: The last name of the person. This is typically used in combination with the first_name parameter.
        name: The full name of the person. This will typically be a first name and last name separated by a space.
            If you use this parameter, you do not need to use the first_name and last_name parameters.
        email: The email address of the person.
        organization_name: The name of the person's employer. This can be the current employer or a previous employer.
        domain: The domain name for the person's employer. This can be the current employer or a previous employer.
            Do not include www., the @ symbol, or similar.
        id: The Apollo ID for the person. Each person in the Apollo database is assigned a unique ID.
        linkedin_url: The URL for the person's LinkedIn profile.
        reveal_personal_emails: Set to true if you want to enrich the person's data with personal emails.
            This potentially consumes credits as part of your Apollo pricing plan. Default is false.
        reveal_phone_number: Set to true if you want to enrich the person's data with all available phone numbers,
            including mobile phone numbers. This potentially consumes credits as part of your Apollo pricing plan.
            Default is false. If this parameter is set to true, you must enter a webhook URL for webhook_url.
        webhook_url: If you set the reveal_phone_number parameter to true, this parameter becomes mandatory.
            Enter the webhook URL that specifies where Apollo should send a JSON response with the phone number.

    Returns:
        Enriched person data in YAML format
    """

    # build request parameters in a simple way
    params = {
        k: v
        for k, v in {
            "first_name": first_name,
            "last_name": last_name,
            "name": name,
            "email": email,
            "organization_name": organization_name,
            "domain": domain,
            "id": id,
            "linkedin_url": linkedin_url,
            "reveal_personal_emails": reveal_personal_emails if reveal_personal_emails else None,
            "reveal_phone_number": reveal_phone_number if reveal_phone_number else None,
            "webhook_url": webhook_url if reveal_phone_number else None,
        }.items()
        if v is not None
    }

    if params.get("reveal_phone_number") and not params.get("webhook_url"):
        return "error: webhook_url is required when reveal_phone_number is true"

    logger.info(f"enrich person with params: {params}")

    result = await apollo.enrich_person(**params)

    if "error" in result:
        return f"error: enrich person failed, {result['error']}"

    logger.info("person enrichment successful")
    return f"```yaml\n{yaml.dump(result, indent=2, allow_unicode=True)}\n```"


@tool(parse_docstring=True)
async def get_complete_organization_info(
    id: str | None = None,
    name: str | None = None,
    location: str | None = None,
    website_url: str | None = None,
) -> str:
    """
    Get complete organization information by ID, name, location, or website URL.
    If ID is provided, directly fetch the organization details.
    If ID is not provided, use an AI agent to search for the organization first, then get detailed information.

    Args:
        id: The unique Apollo ID of the organization
        name: The name of the organization to search for
        location: The location of the organization (city, state, or country)
        website_url: The website URL of the organization

    Returns:
        Complete organization information in YAML format
    """

    result = await apollo.get_complete_organization_info(
        id=id,
        name=name,
        location=location,
        website_url=website_url,
    )
    return f"```yaml\n{yaml.dump(result, indent=2)}\n```"


if __name__ == "__main__":
    import dotenv

    dotenv.load_dotenv()

    async def main():
        # test organization search
        org_result = await apollo.search_organization(q_organization_name="Pilot Air")
        print("Organization Search Results:")
        print(yaml.dump(org_result, indent=2))

        # test people search
        if org_result and org_result.data and len(org_result.data) > 0 and not org_result.error:
            people_result = await apollo.search_people(
                organization_ids=[org_result.data[0]["id"]], person_titles=["CEO", "CTO"], page=1
            )
            print("\nPeople Search Results:")
            print(yaml.dump(people_result, indent=2))

    asyncio.run(main())
