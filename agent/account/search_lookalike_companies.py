import asyncio
import json
from pathlib import Path
from typing import Annotated, List, Optional

import yaml
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState, create_react_agent
from loguru import logger

from agent.account.analyze_company_similarity import bulk_analyze_company_similarity
from agent.account.models import SearchCompaniesState
from agent.tools.apollo import apollo
from agent.tools.tavily import tavily_search
from utils import init_model


@tool(parse_docstring=True)
async def search_organizations(
    q_organization_keyword_tags: list[str] | None = None,
    organization_locations: list[str] | None = None,
    organization_num_employees_ranges: list[str] | None = None,
    excludes: Annotated[List[str], InjectedState("excludes")] = [],
) -> str:
    """
    Use the Organization Search endpoint to find companies in the Apollo database. Several filters are available to help narrow your search.
    This method only provides company name, company website, company headquarters address, and LinkedIn company homepage URL.
    All of the params must be in English.

    Args:
        q_organization_keyword_tags: Filter search results based on keywords associated with companies.
        organization_locations: The location of the company headquarters.
        organization_num_employees_ranges: The number range of employees working for the company.
        excludes: The list of company names to exclude from the search, which is injected from the state.

    Returns:
        Search results in YAML format
    """  # noqa: E501

    result = []
    page = 1

    # use 50 as default limit to lower the agent's cost, and the apollo api has a limit of 100
    # at most times, only one api call is needed even after filtering some companies
    default_limit = 50

    try:
        while len(result) < default_limit:
            logger.info(
                f"Searching organizations, page: {page},"
                f"q_organization_keyword_tags: {q_organization_keyword_tags},"
                f"organization_locations: {organization_locations},"
                f"organization_num_employees_ranges: {organization_num_employees_ranges},"
                f"excludes: {excludes}",
            )
            response = await apollo.search_organization(
                q_organization_keyword_tags=q_organization_keyword_tags,
                organization_locations=organization_locations,
                organization_num_employees_ranges=organization_num_employees_ranges,
                exclude_organization_names=excludes,
                page=page,
            )

            if response.error:
                return f"Error: {response.error}"

            organizations = response.data or []
            result.extend(organizations)

            # if has more pages, continue to search, and the total pages is not 1
            if response.pagination and response.pagination.total_pages > 1:
                page += 1
            else:
                break
    except Exception as e:
        logger.error(f"Error searching organizations: {str(e)}")
        return f"Error: {str(e)}"

    return (
        f"Total {len(result)} organizations found\n\n```yaml\n"
        f"{yaml.dump(result[:default_limit], indent=2, allow_unicode=True)}\n```"
    )


async def search_lookalike_companies(
    user_query: str,
    reference_company: str,
    excludes: Optional[List[str]] = [],
    limit: Optional[int] = 10,
) -> dict:
    """
    根据用户查询需求和模板公司详细信息，搜索lookalike公司列表，并返回筛选后的lookalike公司列表

    Args:
        user_query: 用户查询
        reference_company: 模板公司详细信息
        excludes: 排除的公司列表
        limit: 限制返回公司的数量，默认10

    Returns:
        dict: {"title": str, "lookalike_companies": list[dict]}
    """
    logger.info("Starting search for lookalike companies...")

    llm = init_model(
        model="gemini-2.5-flash",
        max_tokens=20480,
        temperature=0,
        include_thoughts=True,
        thinking_budget=256,
    )
    tools = [search_organizations, tavily_search, bulk_analyze_company_similarity]

    system_prompt = (Path(__file__).parent / "prompts" / "search_lookalike_companies.md").read_text()
    if not system_prompt:
        raise ValueError("Unable to load system prompt file")

    prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")])

    agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt,
        state_schema=SearchCompaniesState,
        name="search_lookalike_companies_agent",
    )

    input_message = (
        f"## 用户查询需求：\n{user_query}\n\n## 模板公司信息：\n{reference_company}\n\n## 限制返回的公司数量：{limit}"
    )
    if excludes and len(excludes) > 0:
        input_message += f"\n\n## 需要排除的公司列表：\n{json.dumps(excludes)}"

    result = await agent.ainvoke(
        input={
            "messages": [("human", input_message)],
            "user_query": user_query,
            "reference_company": reference_company,
            "excludes": excludes,
            "limit": limit,
            "lookalike_companies": [],
        },
        config=RunnableConfig(recursion_limit=100),
    )

    lookalike_companies = result.get("lookalike_companies", [])
    logger.info(f"Lookalike companies search completed, found {len(lookalike_companies)} lookalike companies")

    return {"title": user_query, "lookalike_companies": lookalike_companies}


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()

    async def main():
        # 测试数据
        user_query = "I want to find some companies similar Telsim that use 5G mobile networks"
        ideal_customer_profile = "### 1. 客户画像梳理\n\n*   **基础信息**\n    *   **行业**：电信（移动通信、互联网服务）\n    *   **规模**：员工数约 26 人（小型企业）\n    *   **地域**：澳大利亚\n    *   **发展阶段**：成立于 2020 年，处于成长阶段\n*   **组织特征**\n    *   **业务模式**：提供预付费移动计划、NBN 家庭互联网服务、eSIM 国际计划。与主要网络（如 Telstra、nbn）合作提供连接服务。\n    *   **组织架构**：专注于客户满意度，提供灵活服务，无合约锁定。\n*   **技术环境**\n    *   **现有技术栈**：Amazon AWS, Apache, Bootstrap Framework, Google Tag Manager, WordPress.org, Zoho One 等。\n    *   **数字化程度**：提供在线购买、自助服务门户，数字化程度较高。\n    *   **IT 预算**：中小型企业，IT 预算可能相对有限，注重成本效益。\n*   **Keywords**：telecom, mobile sim, prepaid, postpaid, sim only plans, sim cards, cheap simcards, data only plan, 5g network, esim, unlimited data, mobile connectivity, international roaming, home internet, 4g lte, nbn service, mobile network, telstra network, travel esims, telecommunication solutions, digital sim.\n\n### 2. 痛点深度挖掘\n\n*   **业务痛点**\n    *   需要高性价比、灵活的移动通信和互联网解决方案。\n    *   对传统电信合约的束缚感到不满。\n    *   国际旅行时需要便捷、经济的漫游解决方案。\n    *   寻求可靠且覆盖范围广的网络服务。\n*   **技术痛点**\n    *   需要支持 5G 网络的高速连接。\n    *   希望简化 SIM 卡管理，倾向于 eSIM 解决方案。\n    *   需要易于激活和管理的电信服务。\n*   **成本痛点**\n    *   寻求更低的移动和互联网服务成本。\n    *   避免因合约锁定带来的额外费用。\n*   **痛点优先级**：高性价比和灵活性 > 5G 高速连接 > 国际漫游便利性 > 易于管理。\n\n### 3. 关键成功特征\n\n*   **触发事件**：用户寻求新的、更经济的移动或家庭互联网服务；国际旅行需求；对现有运营商服务不满。\n*   **应用场景**：个人用户、小型办公室/家庭办公室（SOHO）、中小企业寻求高速 5G LTE 连接解决方案。\n*   **决策因素**：价格竞争力、网络覆盖（Telstra 网络）、5G 支持、无合约锁定、灵活的套餐选项、eSIM 便利性、客户服务。\n\n### 4. Lookalike 筛选条件\n\n*   **必备条件**\n    *   **行业**：电信（移动通信服务提供商、互联网服务提供商）。\n    *   **规模**：员工数在 20-100 人之间（小型到中型企业）。\n    *   **地域**：澳大利亚。\n    *   **产品/服务**：提供 5G 移动网络服务。\n*   **优选条件**\n    *   提供预付费移动计划或无合约服务。\n    *   提供家庭互联网服务（如 NBN）。\n    *   提供 eSIM 服务。\n    *   与主要网络运营商合作。\n    *   注重客户满意度和灵活服务。\n*   **排除条件**\n    *   仅提供固定电话服务或传统企业级通信解决方案的公司。\n    *   大型跨国电信巨头（除非其特定部门符合小型企业特征）。\n*   **验证指标**\n    *   公司网站明确提及 5G 服务。\n    *   产品页面显示预付费或无合约移动套餐。\n    *   新闻稿或公司简介中提及与主要网络运营商的合作。\n    *   公司规模（员工人数）与 Telsim 相似。\n    *   公司所在地为澳大利亚。"  # noqa: E501
        company_info = {"name": "Telsim Australia & New Zealand", "website": "http://www.telsim.com.au"}
        excludes = ["Inhand Networks", "Yomojo", "Boost Mobile - Australia", "TeleChoice", "Belong", "amaysim"]

        result = await search_lookalike_companies(
            user_query=user_query,
            reference_company=f"{yaml.dump(company_info)}\n\n{ideal_customer_profile}",
            excludes=excludes,
        )
        logger.info(f"search_lookalike_companies result: {result}")

    asyncio.run(main())
