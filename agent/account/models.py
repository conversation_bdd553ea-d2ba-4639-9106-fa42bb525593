from enum import Enum
from typing import List, Optional

from langgraph.prebuilt.chat_agent_executor import AgentState
from pydantic import BaseModel, Field


class AccountTypeEnum(str, Enum):
    EU = "EU(End User)"
    OEM = "OEM"
    SI = "SI(System Integrator)"
    DIS = "DIS(Distributor)"
    OPERATOR = "Operator(Fleet or Vending Operator)"
    SALES_REP = "Sales Rep"
    TELECOM_OPERATOR = "Telecom Operator"
    PARTNER = "Partner"
    IOT_PLATFORM = "IoT Platform"
    MSP = "MSP"
    VAR_RESELLER = "VAR Reseller"
    OTHERS = "Others"


class IndustryEnum(str, Enum):
    ENERGY = "Energy"
    INDUSTRY = "Industry"
    COMMERCE = "Commerce"
    ICT = "ICT"
    MOBILITY = "Mobility"
    CITY = "City"
    OTHERS = "Others"


class MarketSegmentEnum(str, Enum):
    DA = "DA"
    ENERGY_MGT = "Energy Mgt."
    RENEWABLE = "Renewable"
    METERING = "Metering"
    AUTOMATION = "Automation"
    BUILDING = "Building"
    RTM = "RTM"
    ATM = "ATM"
    POS = "POS"
    KIOSK = "Kiosk"
    DIGITAL_SIGNAGE = "Digital Signage"
    VENDING = "Vending"
    BUSINESS_NETWORKING = "Business Networking"
    TELECOM = "Telecom"
    IOT = "IoT"
    IT_GENERAL = "IT(General IT)"
    FLEET = "Fleet"
    COLD_CHAIN = "Cold Chain"
    TRAFFIC = "Traffic"
    TRANSPORTATION = "Transportation"
    RAILWAY = "Railway"
    PARKING = "Parking"
    EB_CHARGING = "EB Charging"
    LIGHTING = "Lighting"
    SECURITY = "Security"
    HEALTHCARE = "Healthcare"
    EDUCATION = "Education"
    AGRICULTURE = "Agriculture"
    GOVERNMENT = "Government"


class LookalikeCompanyInfo(BaseModel):
    """公司信息结构"""

    name: str = Field(description="公司名称")
    website: str = Field(description="公司官网")
    linkedin: Optional[str] = Field(default="", description="公司LinkedIn链接")
    account_type: AccountTypeEnum = Field(
        default=AccountTypeEnum.OTHERS,
        description="""公司类型，必须从给定的选项中选择最匹配的一项。判断标准：
        - EU(End User): 最终用户，直接使用产品或服务的公司，如制造企业、零售企业等
        - OEM: 原始设备制造商，将产品集成到自己的设备中再销售的公司
        - SI(System Integrator): 系统集成商，为客户提供系统集成解决方案的公司
        - DIS(Distributor): 分销商，负责产品销售和渠道管理的公司
        - Operator(Fleet or Vending Operator): 运营商，如车队运营商、自动售货机运营商等
        - Sales Rep: 销售代表，专门负责销售的公司或个人
        - Telecom Operator: 电信运营商，如移动运营商、宽带服务商等
        - Partner: 合作伙伴，与公司有合作关系但不属于上述类型的公司
        - IoT Platform: IoT平台提供商，提供物联网平台服务的公司
        - MSP: 托管服务提供商，提供IT托管服务的公司
        - VAR Reseller: 增值经销商，在销售过程中提供增值服务的公司
        - Others: 其他类型，当无法明确归类时使用""",
    )
    phone: Optional[str] = Field(default="", description="公司联系电话")
    industry: IndustryEnum = Field(
        default=IndustryEnum.OTHERS,
        description="""公司所属行业，必须从给定的选项中选择最匹配的一项。判断标准：
        - Energy: 能源行业，包括电力、石油、天然气、可再生能源等
        - Industry: 工业制造，包括制造业、重工业、轻工业等
        - Commerce: 商业贸易，包括零售、批发、贸易等
        - ICT: 信息通信技术，包括IT服务、软件开发、通信设备等
        - Mobility: 移动出行，包括汽车、交通、物流等
        - City: 智慧城市，包括城市管理、公共服务等
        - Others: 其他行业，当无法明确归类时使用""",
    )
    market_segments: List[MarketSegmentEnum] = Field(
        default=[MarketSegmentEnum.DA],
        description="""行业细分领域，必须从给定的选项中选择最匹配的一项或多项（可多选）。判断标准：
        - DA: 数据采集相关（无法明确归类时可使用此选项）
        - Energy Mgt.: 能源管理
        - Renewable: 可再生能源
        - Metering: 计量设备
        - Automation: 自动化
        - Building: 建筑智能化
        - RTM: 实时监控
        - ATM: 自动取款机
        - POS: 销售点终端
        - Kiosk: 自助服务终端
        - Digital Signage: 数字标牌
        - Vending: 自动售货
        - Business Networking: 商业网络
        - Telecom: 电信
        - IoT: 物联网
        - IT(General IT): 通用IT
        - Fleet: 车队管理
        - Cold Chain: 冷链
        - Traffic: 交通管理
        - Transportation: 运输
        - Railway: 铁路
        - Parking: 停车管理
        - EB Charging: 电动汽车充电
        - Lighting: 照明
        - Security: 安防
        - Healthcare: 医疗健康
        - Education: 教育
        - Agriculture: 农业
        - Government: 政府机构""",
    )
    founded_year: Optional[int] = Field(default=None, description="成立年份")
    organization_revenue: Optional[str] = Field(default="", description="公司营收")
    location: str = Field(default="", description="公司总部地址")
    territory: str = Field(default="", description="公司总部地址（国家），需要设置标准国家名称（仅可设置一个国家）")
    address_state: Optional[str] = Field(
        default="",
        description="公司总部地址（州/省/城市），需要从 location 中截取除国家之外的部分。"
        "对于美国的州，请使用2字母简写（如 CA, NY, TX 等）",
    )
    description: str = Field(default="", description="公司简介，不要超过200字（若字数较多可进行总结）")


class CompanySimilarityAnalysis(BaseModel):
    """公司相似性分析结果"""

    company_name: str = Field(default="", description="公司名称")
    similarity_score: float = Field(default=0.0, description="相似度评分 (0-1), 精确到小数点后两位")
    explanation: Optional[str] = Field(default=None, description="描述相似性或差异")
    error: Optional[str] = Field(default=None, description="错误信息")


class LookalikeCompaniesResult(BaseModel):
    """Lookalike公司搜索结果"""

    title: str = Field(default="", description="标题")
    companies: List[LookalikeCompanyInfo] = Field(default_factory=list, description="Lookalike公司列表")
    error: Optional[str] = Field(default=None, description="错误信息")


class CompanyBasicInfo(BaseModel):
    """单个公司基础信息"""

    id: Optional[str] = Field(default=None, description="公司ID")
    name: str = Field(default=None, description="公司名称")
    website_url: Optional[str] = Field(default=None, description="公司网站")
    linkedin_url: Optional[str] = Field(default=None, description="LinkedIn链接")
    organization_revenue: Optional[str] = Field(default=None, description="公司营收")
    phone: Optional[str] = Field(default=None, description="联系电话")
    founded_year: Optional[int] = Field(default=None, description="成立年份")
    description: Optional[str] = Field(default=None, description="公司描述")
    industry: Optional[str] = Field(default=None, description="公司所属行业")
    location: Optional[str] = Field(default=None, description="公司所在地")


class SearchCompaniesResult(BaseModel):
    """搜索公司结果"""

    companies: List[LookalikeCompanyInfo] = Field(default_factory=list, description="公司列表")
    error: Optional[str] = Field(default=None, description="错误信息")


class SearchCompaniesState(AgentState):
    user_query: str  # 用户查询
    reference_company: str  # 模板公司详细信息
    excludes: list[str]  # 排除的公司列表
    limit: int  # 限制返回的公司数量
    lookalike_companies: list[dict]  # lookalike公司列表


class EnrichedAccountInfo(LookalikeCompanyInfo):
    """丰富后的账户信息"""

    account_email: Optional[str] = Field(default=None, description="公司主要联系邮箱")
    secondary_account_email: Optional[str] = Field(default=None, description="公司次要联系邮箱（选填）")
    secondary_industry: Optional[List[IndustryEnum]] = Field(
        default=None,
        description="公司次要行业（选填），公司主营业务跨多个行业的可选择，必须是以下选项（可多选）：['Energy','Industry','Commerce','ICT','Mobility','City','Others']",
    )
    billing_city: Optional[str] = Field(default=None, description="公司总部所在城市（选填）")
    billing_street: Optional[str] = Field(default=None, description="公司总部所在街道（选填）")
    billing_street_2: Optional[str] = Field(default=None, description="公司总部所在街道 2（选填）")
    billing_code: Optional[str] = Field(default=None, description="公司总部地址的邮编（选填）")

    # 固定默认信息
    stage: str = "Prospect"
    priority: str = "Low"
    lead_source: str = "AI Research"
    labels: str = "sales_agent"
