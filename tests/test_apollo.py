from pprint import pprint

import pytest
import yaml

from agent.tools.apollo import apollo


async def test_search_people():
    query = "<PERSON> | R&D CTO Office @ Schindler US"
    result = await apollo.search_people(q_organization_job_titles=["R&D CTO Office"])
    pprint(result)
    assert "error" not in result


async def test_search_organization():
    result = await apollo.search_organization(
        q_organization_keyword_tags=["air compressor"], organization_locations=["Australia"]
    )
    pprint(result)
    assert result.error is None


async def test_get_organization_by_id():
    result = await apollo.get_organization_by_id("60b2d0545e799d0001881d19")
    pprint(result)
    assert "error" not in result

    print(yaml.dump(result, indent=2))


async def test_get_complete_organization_info_by_id():
    result = await apollo.get_complete_organization_info(id="60b2d0545e799d0001881d19")
    pprint(result)
    assert "error" not in result

    print(yaml.dump(result, indent=2))


async def test_get_complete_organization_info_wrong_name():
    # should raise error when organization info is not found
    with pytest.raises(Exception):
        result = await apollo.get_complete_organization_info(name="Smart Tek LLC", website_url="smartek-its.com")
        pprint(result)


async def test_get_complete_organization_info_by_name():
    result = await apollo.get_complete_organization_info(name="Kaishan Australia", location="Australia")
    pprint(result)
    assert "error" not in result
    assert result.get("website_url") == "http://www.kaishan.com.au"


async def test_get_complete_organization_info_by_name2():
    result = await apollo.get_complete_organization_info(name="Champion Compressors")
    pprint(result)
    assert "error" not in result
    assert result.get("website_url") == "http://www.championcompressors.com.au"


async def test_get_complete_organization_info_wrong_location():
    result = await apollo.get_complete_organization_info(
        name="Vestas Chile Turbinas Eolicas Ltda", website_url="https://www.vestas.com/en", location="Chile"
    )
    pprint(result)
    assert "error" not in result
    assert "www.vestas.com" in result.get("website_url")


async def test_enrich_person():
    # test people enrichment with basic information
    result = await apollo.enrich_person(
        first_name="Tim", last_name="Zheng", organization_name="Apollo", domain="apollo.io"
    )
    pprint(result)
    assert "error" not in result


async def test_enrich_person_with_email():
    # test people enrichment with email
    result = await apollo.enrich_person(name="Tim Zheng", email="<EMAIL>", domain="apollo.io")
    pprint(result)
    assert "error" not in result


async def test_enrich_people_phone():
    from apollo.apollo_api import enrich_people_phone

    result = enrich_people_phone(contact_id="1234567890", linkedin_url="https://www.linkedin.com/in/brian-elias")
    pprint(result)
    assert "error" not in result
