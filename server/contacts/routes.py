import json
import uuid
from pprint import pprint

import yaml
from fastapi import (
    APIR<PERSON>er,
    HTTPException,
)
from fastapi.responses import StreamingResponse
from loguru import logger
from pydantic import BaseModel, Field

from agent.tools.apollo import apollo
from agent.tools.linkedin import agentic_search_people
from config import SERVER_DOMAIN
from server.common_types import StreamMessage
from server.contacts.contact_enrichment_requests import contact_enrichment_requests
from server.contacts.handle_apollo_contact_webhook import handle_apollo_contact_webhook
from utils.extractors import extract_structure_response
from zoho.browser_api import ZohoBrowserAPI
from zoho.contacts_api import get_contact_by_id
from zoho.models import ZohoContactData

router = APIRouter(tags=["contacts"])


@router.get("/contact/{contact_id}/enrich/status")
async def get_contact_enrich_status(contact_id: str):
    """
    根据 contact_id 获取联系人扩充信息请求状态和存储的所有数据

    Return:
    {
        "status": "pending", # 状态: not_found, pending, success, failed
        "updated_at": "2025-06-09T13:27:39.687886", # 状态更新时间
        "latest_data": {...}, # Apollo扩充数据（如果存在）
    }
    """
    try:
        enrichment_data = contact_enrichment_requests.get_contact_enrichment_data(contact_id)
        if enrichment_data is None:
            return {"status": "not_found", "error": "Contact not found"}
        return enrichment_data
    except Exception as e:
        logger.error(f"获取联系人 {contact_id} 扩充状态失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail={"error": "internal_error", "message": str(e)})


# 手机号的信息，直接更新
# 除了手机号的信息，返回结果，选择更新


@router.get("/contact/{contact_id}/enrich")
async def enrich_contact_info(contact_id: str):
    """
    根据 contact_id 获取联系人扩充信息
    """
    # 校验是否正在获取中
    try:
        contact_enrichment_status = contact_enrichment_requests.get_contact_enrichment_data(contact_id)
        if contact_enrichment_status and contact_enrichment_status.get("status", None) == "pending":
            # 扩充请求未完成，不允许重复请求
            raise HTTPException(
                status_code=400,
                detail={"error": "pending", "messages": "Contact is being enriched"},
            )
    except Exception as e:
        logger.error(f"检查联系人 {contact_id} 扩充状态失败: {e}", exc_info=True)
        # 如果查询失败，继续执行扩充流程

    # 从zoho中获取联系人信息
    contact_zoho_info = await get_contact_by_id(contact_id)

    if contact_zoho_info is None:
        # 联系人不存在
        raise HTTPException(status_code=400, detail={"error": "not_found", "messages": "Contact not found"})

    linkedin_url = contact_zoho_info.get("linkedIn")

    if not linkedin_url:
        # if not linkedin_url, search by name from LinkedIn
        searched_person = await agentic_search_people(
            yaml.dump(contact_zoho_info, allow_unicode=True),
        )
        logger.info(f"searched person from LinkedIn: {searched_person}")
        linkedin_url = searched_person.profile_url

    webhook_request_id = str(uuid.uuid4())
    webhook_url = f"{SERVER_DOMAIN}/api/sales-agent-webhook/apollo/{webhook_request_id}"

    reveal_phone_number = False if contact_zoho_info.get("phone") or contact_zoho_info.get("mobile") else True

    person = await apollo.enrich_person(
        linkedin_url=linkedin_url,
        reveal_phone_number=reveal_phone_number,
        webhook_url=webhook_url,
    )

    if not person:
        # 扩充请求失败
        raise HTTPException(
            status_code=400,
            detail={"error": "failed", "messages": "Failed to enrich contact"},
        )

    for key in ["employment_history", "organization"]:
        person.pop(key)
    person_str = yaml.dump(person, allow_unicode=True)

    logger.info(f"person_str: {person_str}")

    apollo_contact = await extract_structure_response(
        person_str,
        ZohoContactData,
        instructions="""
    - Unless explicitly asked, only output the value if you are very confident about it, don't make any assumptions.
    - Derive the zip code from state and city.
            """,
    )

    if not apollo_contact:
        # 解析联系人信息失败
        raise HTTPException(
            status_code=400,
            detail={"error": "failed", "messages": "Failed to parse enriched contact data"},
        )

    try:
        all_data = contact_enrichment_requests.store_enriched_data(
            contact_id=contact_id,
            request_id=webhook_request_id,
            status="success",  # 状态: pending, success, failed
            # status="pending",
            enriched_data=apollo_contact,
            owner=contact_zoho_info.get("owner", {}),
        )

        return all_data
    except Exception as e:
        logger.error(f"存储联系人 {contact_id} 扩充数据失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail={"error": "storage_failed", "message": "Failed to store enriched data"}
        )


@router.post("/webhook/apollo/{request_id}")
async def handle_apollo_webhook(request_id: str, request: dict):
    return await handle_apollo_contact_webhook(request_id, request)


class QueryZohoContactDataRequest(BaseModel):
    iamadt: str = Field(..., description="The iamadt")
    iambdt: str = Field(..., description="The iambdt")
    csrf_token: str = Field(..., description="The csrf_token")


@router.post("/contact/{contact_id}/data")
async def get_contact_origin_data(contact_id: str, request: QueryZohoContactDataRequest):
    """
    获取联系人原始数据
    """
    try:
        client = ZohoBrowserAPI(request.iamadt, request.iambdt, request.csrf_token)
        contact_data = await client.get_contact_by_id(contact_id)
        if contact_data is None:
            raise HTTPException(status_code=404, detail={"error": "not_found", "message": "Contact not found"})
        logger.info(f"contact_data: {contact_data}")
        return contact_data

    except Exception as e:
        logger.error(f"获取联系人 {contact_id} 原始数据失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail={"error": "internal_error", "message": str(e)})


class UpdateContactDataRequest(BaseModel):
    iamadt: str = Field(..., description="The iamadt")
    iambdt: str = Field(..., description="The iambdt")
    csrf_token: str = Field(..., description="The csrf_token")
    contact: dict = Field(..., description="The contact")


@router.post("/contact/{contact_id}/update")
async def update_contact_data(contact_id: str, request: UpdateContactDataRequest):
    """
    更新联系人数据
    """
    try:
        iamadt = request.iamadt
        iambdt = request.iambdt
        csrf_token = request.csrf_token
        contact = request.contact

        client = ZohoBrowserAPI(iamadt, iambdt, csrf_token)
        logger.info(f"contact_data: {pprint(contact)}")
        contact_data = await client.update_contact_by_id(contact_id, contact)
        if contact_data is None:
            raise HTTPException(status_code=404, detail={"error": "not_found", "message": "Contact not found"})
        logger.info(f"contact_data: {contact_data}")
        return contact_data.get("data", {})[0]

    except Exception as e:
        logger.error(f"更新联系人 {contact_id} 数据失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail={"error": "internal_error", "message": str(e)})


class IntelligentAddContactRequest(BaseModel):
    user_input: str = Field(..., description="The user input for intelligent contact addition")
    account_id: str = Field(..., description="The account ID in the CRM")


@router.post("/contact/intelligent-add")
async def intelligent_add_contact(request: UpdateContactDataRequest):
    """
    Smartly add a contact based on user input and account ID.
    """

    def _build_stream_message(message: StreamMessage) -> str:
        return f"data: {json.dumps(message.model_dump(exclude_none=True), ensure_ascii=False, default=str)}\n\n"

    async def generate_smart_create_sse():
        try:
            user_input = request.user_input
            account_id = request.account_id

            if not user_input:
                error_msg = StreamMessage.from_exception(Exception("User input is required"))
                yield _build_stream_message(error_msg)
                return

            if not account_id:
                error_msg = StreamMessage.from_exception(Exception("Account ID is required"))
                yield _build_stream_message(error_msg)
                return

            # 这里调用智能添加联系人逻辑
            zoho_contact_data = await apollo.smart_add_contact(user_input, account_id)

            if not zoho_contact_data:
                error_msg = StreamMessage.from_exception(Exception("Failed to add contact"))
                yield _build_stream_message(error_msg)
                return

            success_msg = StreamMessage(status="success", data=zoho_contact_data, message="Contact added successfully")
            yield _build_stream_message(success_msg)

        except Exception as e:
            error_msg = StreamMessage.from_exception(e)
            yield _build_stream_message(error_msg)

    return StreamingResponse(
        content=generate_smart_create_sse(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )
